<template>
  <div>
    <tab-title title="lang.rms.fed.container" />

    <search-box ref="searchBox" />

    <panel-shelf v-if="containerType == 'shelf'" class="container-item" />

    <panel-rack
      v-if="containerType == 'rack' || mapConfig?.allRackContainer"
      @rectEnd="rectEnd"
      class="container-item"
    />
    <!-- 添加托盘支架组件 -->
    <panel-pallet-rack v-if="containerType == 'pallet'" @rectEnd="rectEnd" class="container-item" />

    <panel-poppick v-if="containerType == 'poppick'" class="container-item" />

    <panel-x-shelf v-if="containerType == 'xShelf'" class="container-item" />
  </div>
</template>

<script>
import { mapState, mapActions } from "pinia";
import { useMap2dStore } from "@map2d/dataStore/index";
import { useMap2dContainerStore } from "@map2d/dataStore/right-panel/container";
import TabTitle from "@map2d/packages/common/tab-title.vue";
import SearchBox from "./search-box.vue";
import PanelShelf from "./shelf/index.vue";
import PanelRack from "./rack/index.vue";
import PanelPoppick from "./poppick/index.vue";
import PanelXShelf from "./xShelf/index.vue";
// 导入托盘支架组件
import PanelPalletRack from "./palletRack/index.vue";

export default {
  name: "map-container",
  components: {
    TabTitle,
    SearchBox,
    PanelShelf,
    PanelRack,
    PanelPoppick,
    PanelXShelf,
    PanelPalletRack,
  },
  computed: {
    ...mapState(useMap2dStore, ["isRightOccupy", "mapClickData", "mapConfig"]),
    ...mapState(useMap2dContainerStore, ["containerType"]),
  },
  watch: {
    mapClickData: {
      handler(data) {
        if (this.isRightOccupy) return;
        if (!["shelf", "rack", "poppick", "xShelf", "pallet"].includes(data?.layer)) return;
        this.setSearchData(null);
        const shelfType = data?.options?.shelfType;
        switch (shelfType) {
          case "PPP_SHELF":
            this.setContainerType("poppick");
            break;
          case "X_HOLDER":
          case "X_HOLDER_PALLET":
          case "X_HOLDER_PALLET_STACK":
          case "X_PALLET_STACK":
          case "X_PALLET":
            this.setContainerType("xShelf");
            break;
          case "PALLET_RACK":
            this.setContainerType("pallet");
            break;
          default:
            this.setContainerType(data?.layer);
            break;
        }
      },
      immediate: true,
    },
  },
  created() {},
  beforeUnmount() {
    this.resetContainerStoreData();
  },

  methods: {
    ...mapActions(useMap2dContainerStore, [
      "setContainerType",
      "resetContainerStoreData",
      "setSearchData",
    ]),

    rectEnd() {
      this.$refs.searchBox.rectEnd();
    },
  },
};
</script>

<style lang="less" scoped>
.container-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}
</style>
